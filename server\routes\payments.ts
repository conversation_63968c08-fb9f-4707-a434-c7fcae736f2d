const express = require('express');
const axios = require('axios');
const { supabase } = require('../lib/supabase');
const { updateUserSubscription } = require('../services/subscription');

const router = express.Router();
const PAYSTACK_SECRET_KEY = process.env.PAYSTACK_SECRET_KEY;

// Endpoint to verify a payment
router.post('/verify', async (req, res) => {
  try {
    const { reference } = req.body;

    if (!reference) {
      return res.status(400).json({
        success: false,
        message: 'Payment reference is required'
      });
    }

    // Call Paystack API to verify the transaction
    const response = await axios.get(
      `https://api.paystack.co/transaction/verify/${reference}`,
      {
        headers: {
          Authorization: `Bearer ${PAYSTACK_SECRET_KEY}`
        }
      }
    );

    const { data } = response.data;

    // Check if payment was successful
    if (data.status === 'success') {
      // Extract payment details
      const {
        amount,
        customer: { email },
        metadata: { custom_fields }
      } = data;

      // Find the plan from custom fields
      const planField = custom_fields.find(field => field.variable_name === 'plan');
      const planId = planField ? planField.value : null;

      if (!planId) {
        return res.status(400).json({
          success: false,
          message: 'Plan information missing'
        });
      }

      // Update user subscription in database
      const subscriptionResult = await updateUserSubscription(email, planId, amount, reference);

      if (subscriptionResult.success) {
        return res.status(200).json({
          success: true,
          message: 'Payment verified and subscription updated',
          data: subscriptionResult.data
        });
      } else {
        return res.status(500).json({
          success: false,
          message: 'Payment verified but failed to update subscription',
          error: subscriptionResult.error
        });
      }
    } else {
      return res.status(400).json({
        success: false,
        message: 'Payment verification failed',
        data: data
      });
    }
  } catch (error) {
    console.error('Payment verification error:', error);
    return res.status(500).json({
      success: false,
      message: 'Server error during payment verification',
      error: error.message
    });
  }
});

module.exports = router;
