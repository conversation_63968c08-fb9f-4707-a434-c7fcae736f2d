import { describe, it, expect, vi, beforeEach } from 'vitest';
import { renderHook, act } from '@testing-library/react';
import { useCustomAuth } from '@/hooks/use-custom-auth';
import { supabase } from '@/integrations/supabase/client';
import * as brevoService from '@/services/brevo-direct-service';

// Mock Supabase and Brevo service
vi.mock('@/integrations/supabase/client', () => ({
  supabase: {
    auth: {
      signUp: vi.fn(),
      resetPasswordForEmail: vi.fn(),
    },
  },
}));

vi.mock('@/services/brevo-direct-service', () => ({
  sendVerificationEmail: vi.fn(),
  sendPasswordResetEmail: vi.fn(),
}));

describe('useCustomAuth hook', () => {
  beforeEach(() => {
    // Reset all mocks before each test
    vi.clearAllMocks();
    
    // Mock window.location
    Object.defineProperty(window, 'location', {
      value: {
        origin: 'http://localhost:3000',
      },
      writable: true,
    });
  });

  it('initializes with loading=false and error=null', () => {
    const { result } = renderHook(() => useCustomAuth());
    
    expect(result.current.loading).toBe(false);
    expect(result.current.error).toBe(null);
  });

  describe('signUp function', () => {
    it('signs up a user successfully', async () => {
      // Mock successful responses
      vi.mocked(supabase.auth.signUp).mockResolvedValue({
        data: {
          user: {
            id: '123',
            email: '<EMAIL>',
            confirmation_token: 'test-token',
          },
        },
        error: null,
      } as any);
      
      vi.mocked(brevoService.sendVerificationEmail).mockResolvedValue({
        success: true,
      });
      
      const { result } = renderHook(() => useCustomAuth());
      
      let signUpResult;
      await act(async () => {
        signUpResult = await result.current.signUp('<EMAIL>', 'password123', { name: 'Test User' });
      });
      
      // Check loading state during and after operation
      expect(result.current.loading).toBe(false);
      expect(result.current.error).toBe(null);
      
      // Check function was called with correct params
      expect(supabase.auth.signUp).toHaveBeenCalledWith({
        email: '<EMAIL>',
        password: 'password123',
        options: {
          data: { name: 'Test User' },
          emailRedirectTo: 'http://localhost:3000/auth/callback',
        },
      });
      
      // Check verification email was sent
      expect(brevoService.sendVerificationEmail).toHaveBeenCalledWith(
        '<EMAIL>',
        'http://localhost:3000/auth/verify?token=test-token'
      );
      
      // Check result
      expect(signUpResult).toEqual({
        success: true,
        message: 'Account created! Please check your email to verify your account.',
        data: {
          user: {
            id: '123',
            email: '<EMAIL>',
            confirmation_token: 'test-token',
          },
        },
      });
    });

    it('handles sign up error from Supabase', async () => {
      // Mock error response from Supabase
      vi.mocked(supabase.auth.signUp).mockResolvedValue({
        data: null,
        error: new Error('Email already in use'),
      } as any);
      
      const { result } = renderHook(() => useCustomAuth());
      
      let signUpResult;
      await act(async () => {
        signUpResult = await result.current.signUp('<EMAIL>', 'password123');
      });
      
      // Check error state
      expect(result.current.loading).toBe(false);
      expect(result.current.error).toBe('Email already in use');
      
      // Check result
      expect(signUpResult).toEqual({
        success: false,
        error: 'Email already in use',
      });
      
      // Verify email was not sent
      expect(brevoService.sendVerificationEmail).not.toHaveBeenCalled();
    });

    it('handles email sending error', async () => {
      // Mock successful Supabase response but failed email
      vi.mocked(supabase.auth.signUp).mockResolvedValue({
        data: {
          user: {
            id: '123',
            email: '<EMAIL>',
            confirmation_token: 'test-token',
          },
        },
        error: null,
      } as any);
      
      vi.mocked(brevoService.sendVerificationEmail).mockResolvedValue({
        success: false,
        error: 'Failed to send email',
      });
      
      const { result } = renderHook(() => useCustomAuth());
      
      let signUpResult;
      await act(async () => {
        signUpResult = await result.current.signUp('<EMAIL>', 'password123');
      });
      
      // Check error state
      expect(result.current.loading).toBe(false);
      expect(result.current.error).toBe('Failed to send email');
      
      // Check result
      expect(signUpResult).toEqual({
        success: false,
        error: 'Failed to send email',
      });
    });
  });

  describe('resetPassword function', () => {
    it('sends password reset email successfully', async () => {
      // Mock successful responses
      vi.mocked(supabase.auth.resetPasswordForEmail).mockResolvedValue({
        data: {},
        error: null,
      } as any);
      
      vi.mocked(brevoService.sendPasswordResetEmail).mockResolvedValue({
        success: true,
      });
      
      const { result } = renderHook(() => useCustomAuth());
      
      let resetResult;
      await act(async () => {
        resetResult = await result.current.resetPassword('<EMAIL>');
      });
      
      // Check loading state
      expect(result.current.loading).toBe(false);
      expect(result.current.error).toBe(null);
      
      // Check function was called with correct params
      expect(supabase.auth.resetPasswordForEmail).toHaveBeenCalledWith(
        '<EMAIL>',
        {
          redirectTo: 'http://localhost:3000/auth/reset-password',
        }
      );
      
      // Check reset email was sent
      expect(brevoService.sendPasswordResetEmail).toHaveBeenCalledWith(
        '<EMAIL>',
        'http://localhost:3000/auth/reset-password?email=test%40example.com'
      );
      
      // Check result
      expect(resetResult).toEqual({
        success: true,
        message: 'Password reset instructions sent to your email.',
        data: {},
      });
    });

    it('handles reset password error from Supabase', async () => {
      // Mock error response from Supabase
      vi.mocked(supabase.auth.resetPasswordForEmail).mockResolvedValue({
        data: null,
        error: new Error('User not found'),
      } as any);
      
      const { result } = renderHook(() => useCustomAuth());
      
      let resetResult;
      await act(async () => {
        resetResult = await result.current.resetPassword('<EMAIL>');
      });
      
      // Check error state
      expect(result.current.loading).toBe(false);
      expect(result.current.error).toBe('User not found');
      
      // Check result
      expect(resetResult).toEqual({
        success: false,
        error: 'User not found',
      });
      
      // Verify email was not sent
      expect(brevoService.sendPasswordResetEmail).not.toHaveBeenCalled();
    });

    it('handles email sending error for password reset', async () => {
      // Mock successful Supabase response but failed email
      vi.mocked(supabase.auth.resetPasswordForEmail).mockResolvedValue({
        data: {},
        error: null,
      } as any);
      
      vi.mocked(brevoService.sendPasswordResetEmail).mockResolvedValue({
        success: false,
        error: 'Failed to send email',
      });
      
      const { result } = renderHook(() => useCustomAuth());
      
      let resetResult;
      await act(async () => {
        resetResult = await result.current.resetPassword('<EMAIL>');
      });
      
      // Check error state
      expect(result.current.loading).toBe(false);
      expect(result.current.error).toBe('Failed to send email');
      
      // Check result
      expect(resetResult).toEqual({
        success: false,
        error: 'Failed to send email',
      });
    });
  });
});
