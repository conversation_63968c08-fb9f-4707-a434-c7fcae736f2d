import { useState, useEffect } from "react";
import { supabase } from "@/integrations/supabase/client";
import { useAdminUsersTable } from "@/utils/use-existing-tables";

export interface User {
  id: string;
  email: string;
  created_at: string;
  last_sign_in_at: string | null;
  is_subscribed: boolean;
  is_admin: boolean;
  subscription_expires_at: string | null;
  subscription_status?: 'active' | 'expired' | 'free';
}

export function useAdminUsers() {
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchUsers = async () => {
    try {
      setLoading(true);
      setError(null);

      // Use the existing admin_users table
      const result = await useAdminUsersTable();

      if (!result.success) {
        setError(result.error || 'Failed to fetch users');
        setUsers([]);
        return;
      }

      setUsers(result.users);
    } catch (error: any) {
      console.error("Error fetching users:", error);
      setError(error.message || "Failed to fetch users");
    } finally {
      setLoading(false);
    }
  };

  // Update user subscription status
  const updateUserSubscription = async (userId: string, isSubscribed: boolean, expiresAt?: string | null, planId: string = 'pro') => {
    try {
      // Update the users array in memory to ensure UI reflects changes
      setUsers(prevUsers =>
        prevUsers.map(user =>
          user.id === userId
            ? { ...user, is_subscribed: isSubscribed, subscription_expires_at: expiresAt }
            : user
        )
      );

      // Execute SQL to update the database (using the available RPC function)
      try {
        // Use the execute_sql RPC function if available
        const sql = `
          DO $$
          BEGIN
            -- Update profiles table with subscription status
            UPDATE profiles
            SET
              subscription_status = ${isSubscribed ? "'premium'" : "'free'"},
              subscription_ends_at = ${expiresAt ? `'${expiresAt}'` : 'NULL'},
              updated_at = NOW()
            WHERE id = '${userId}';

            -- Also update user_profiles table if it exists
            IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'user_profiles') THEN
              UPDATE user_profiles
              SET
                is_subscribed = ${isSubscribed},
                subscription_expires_at = ${expiresAt ? `'${expiresAt}'` : 'NULL'},
                updated_at = NOW()
              WHERE user_id = '${userId}';

              -- Insert if not exists
              IF NOT FOUND THEN
                INSERT INTO user_profiles (
                  user_id, email, is_subscribed, subscription_expires_at, created_at, updated_at
                )
                SELECT
                  '${userId}',
                  auth.users.email,
                  ${isSubscribed},
                  ${expiresAt ? `'${expiresAt}'` : 'NULL'},
                  NOW(),
                  NOW()
                FROM auth.users
                WHERE auth.users.id = '${userId}'
                ON CONFLICT (user_id) DO NOTHING;
              END IF;
            END IF;

            -- Handle subscriptions table
            IF ${isSubscribed} THEN
              -- Insert or update subscription
              INSERT INTO subscriptions (
                user_id, plan_id, amount_paid, start_date, end_date, is_active, created_at, updated_at
              ) VALUES (
                '${userId}', '${planId}',
                ${planId === 'basic' ? 998 : planId === 'pro' ? 1979 : 5000},
                NOW(), NOW() + INTERVAL '1 year', true, NOW(), NOW()
              )
              ON CONFLICT (user_id) DO UPDATE SET
                plan_id = '${planId}',
                start_date = NOW(),
                end_date = NOW() + INTERVAL '1 year',
                is_active = true,
                updated_at = NOW();
            ELSE
              -- Mark subscription as inactive
              UPDATE subscriptions
              SET is_active = false, updated_at = NOW()
              WHERE user_id = '${userId}';
            END IF;
          END $$;
        `;

        await supabase.rpc('execute_sql', { query: sql });
      } catch (sqlError) {
        console.error("Error executing SQL:", sqlError);
        // Continue anyway, we've already updated the UI
      }

      // Refresh the users list to get the latest data
      await fetchUsers();

      return { success: true };
    } catch (error: any) {
      console.error("Error updating user subscription:", error);
      return { success: false, error: error.message };
    }
  };

  // Update user admin status
  const updateUserAdminStatus = async (userId: string, isAdmin: boolean) => {
    try {
      // Update the users array in memory
      setUsers(prevUsers =>
        prevUsers.map(user =>
          user.id === userId
            ? { ...user, is_admin: isAdmin }
            : user
        )
      );

      // Execute SQL to update the database (using the available RPC function)
      try {
        // Use the is_admin RPC function to check if the current user is an admin
        const { data: isCurrentUserAdmin } = await supabase.rpc('is_admin');

        if (!isCurrentUserAdmin) {
          throw new Error("Only admins can change admin status");
        }

        // Use the execute_sql RPC function if available
        const sql = `
          DO $$
          BEGIN
            -- Handle admin_users table
            IF ${isAdmin} THEN
              -- Insert into admin_users if not exists
              INSERT INTO admin_users (user_id, is_admin)
              VALUES ('${userId}', true)
              ON CONFLICT (user_id) DO NOTHING;
            ELSE
              -- Remove from admin_users
              DELETE FROM admin_users
              WHERE user_id = '${userId}';
            END IF;

            -- Update profiles table
            UPDATE profiles
            SET is_admin = ${isAdmin}, updated_at = NOW()
            WHERE id = '${userId}';

            -- Also update user_profiles table if it exists
            IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'user_profiles') THEN
              -- We don't have is_admin in user_profiles based on the schema
              -- But we'll update the record to keep timestamps current
              UPDATE user_profiles
              SET updated_at = NOW()
              WHERE user_id = '${userId}';
            END IF;
          END $$;
        `;

        await supabase.rpc('execute_sql', { query: sql });
      } catch (sqlError) {
        console.error("Error executing SQL:", sqlError);
        // Continue anyway, we've already updated the UI
      }

      // Refresh the users list
      await fetchUsers();

      return { success: true };
    } catch (error: any) {
      console.error("Error updating user admin status:", error);
      return { success: false, error: error.message };
    }
  };

  // Delete a user (this is a sensitive operation and might require additional permissions)
  const deleteUser = async (userId: string) => {
    try {
      // Check if current user is admin
      const { data: isAdmin, error: adminCheckError } = await supabase.rpc('is_admin');

      if (adminCheckError) {
        console.error("Error checking admin status:", adminCheckError);
        throw new Error("Could not verify admin privileges: " + adminCheckError.message);
      }

      if (!isAdmin) {
        throw new Error("Only admins can delete users");
      }

      // First try to use the delete_user RPC function
      try {
        console.log("Attempting to delete user with ID:", userId);
        const { error: deleteError } = await supabase.rpc('delete_user', { user_id_param: userId });

        if (deleteError) {
          console.error("Error using delete_user RPC function:", deleteError);
          throw new Error("Failed to delete user: " + deleteError.message);
        }
      } catch (rpcError) {
        console.error("RPC delete_user function failed:", rpcError);

        // Fallback to manual deletion if RPC fails
        try {
          console.log("Falling back to manual deletion for user:", userId);

          // First clean up related records
          const cleanupPromises = [
            supabase.from('profiles').delete().eq('id', userId),
            supabase.from('admin_users').delete().eq('user_id', userId),
            supabase.from('user_profiles').delete().eq('user_id', userId),
            supabase.from('subscriptions').delete().eq('user_id', userId),
            supabase.from('payments').delete().eq('user_id', userId),
            supabase.from('quiz_attempts').delete().eq('user_id', userId),
            supabase.from('user_progress').delete().eq('user_id', userId),
            supabase.from('user_quiz_results').delete().eq('user_id', userId),
            supabase.from('feedback').delete().eq('user_id', userId)
          ];

          // Execute all cleanup operations
          await Promise.all(cleanupPromises);

          // Finally, try to delete the user from auth.users using admin API
          const { error: authDeleteError } = await supabase.auth.admin.deleteUser(userId);

          if (authDeleteError) {
            console.error("Error deleting user from auth.users:", authDeleteError);
            throw new Error("Failed to delete user from authentication system: " + authDeleteError.message);
          }
        } catch (manualDeleteError) {
          console.error("Manual deletion failed:", manualDeleteError);
          throw new Error("User deletion failed after multiple attempts. Please try the 'Fix Delete User Function' button and try again.");
        }
      }

      // Refresh the users list
      await fetchUsers();

      return { success: true };
    } catch (error: any) {
      console.error("Error deleting user:", error);
      return { success: false, error: error.message };
    }
  };

  // Fetch users on component mount
  useEffect(() => {
    fetchUsers();
  }, []);

  return {
    users,
    setUsers,
    loading,
    error,
    fetchUsers,
    updateUserSubscription,
    updateUserAdminStatus,
    deleteUser
  };
}
