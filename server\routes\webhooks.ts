import express from 'express';
import crypto from 'crypto';
import { updateUserSubscription } from '../services/subscription';
import { supabase } from '../lib/supabase';
import { timingSafeEqual } from 'crypto';

const router = express.Router();
const PAYSTACK_SECRET_KEY = process.env.PAYSTACK_SECRET_KEY;

// Paystack webhook endpoint
router.post('/paystack', async (req, res) => {
  try {
    // Verify that the request is from Paystack
    const hash = crypto
      .createHmac('sha512', PAYSTACK_SECRET_KEY)
      .update(JSON.stringify(req.body))
      .digest('hex');

    // Use constant-time comparison to prevent timing attacks
    const signature = req.headers['x-paystack-signature'];
    if (!signature || typeof signature !== 'string') {
      return res.status(401).send('Missing signature');
    }

    // Convert both strings to buffers of the same length for comparison
    try {
      const hashBuffer = Buffer.from(hash, 'utf8');
      const signatureBuffer = Buffer.from(signature, 'utf8');

      // If lengths are different, return false but use a timing-safe comparison anyway
      const hashLength = hashBuffer.length;
      const signatureLength = signatureBuffer.length;

      if (hashLength !== signatureLength) {
        return res.status(401).send('Invalid signature');
      }

      // Use timing-safe comparison
      const isEqual = timingSafeEqual(hashBuffer, signatureBuffer);

      if (!isEqual) {
        return res.status(401).send('Invalid signature');
      }
    } catch (error) {
      console.error('Signature verification error:', error);
      return res.status(401).send('Signature verification failed');
    }

    // Process the webhook event
    const { event, data } = req.body;

    switch (event) {
      case 'charge.success':
        // A payment was successful
        const {
          reference,
          amount,
          customer: { email },
          metadata: { custom_fields }
        } = data;

        // Find the plan from custom fields
        const planField = custom_fields.find(field => field.variable_name === 'plan');
        const planId = planField ? planField.value : null;

        if (planId) {
          // Update user subscription
          await updateUserSubscription(email, planId, amount, reference);
          console.log(`Subscription updated for ${email} via webhook`);
        }
        break;

      case 'subscription.create':
        // A subscription was created
        console.log('New subscription created:', data.subscription_code);
        break;

      case 'subscription.disable':
        // A subscription was disabled/cancelled
        if (data.subscription_code) {
          // Find the subscription in your database and mark it as inactive
          const { data: subscriptionData } = await supabase
            .from('subscriptions')
            .select('id')
            .eq('subscription_code', data.subscription_code)
            .single();

          if (subscriptionData) {
            await supabase
              .from('subscriptions')
              .update({
                is_active: false,
                updated_at: new Date().toISOString()
              })
              .eq('id', subscriptionData.id);
          }
        }
        console.log('Subscription disabled:', data.subscription_code);
        break;

      case 'invoice.payment_failed':
        // A subscription renewal payment failed
        console.log('Payment failed for subscription:', data.subscription_code);
        break;

      default:
        // Handle other events as needed
        console.log(`Unhandled event: ${event}`);
    }

    // Return a 200 response to acknowledge receipt of the webhook
    return res.status(200).send('Webhook received');
  } catch (error) {
    console.error('Webhook processing error:', error);
    return res.status(500).send('Webhook processing failed');
  }
});

export default router;
